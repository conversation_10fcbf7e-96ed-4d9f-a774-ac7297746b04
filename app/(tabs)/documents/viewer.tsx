import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
  Dimensions,
  Alert,
  ActivityIndicator,
  Modal,
  TextInput,
} from "react-native";
// Replaced direct WebView usage with a reusable PDFViewer component
import PDFViewer, { PDFViewerRef } from "@/components/pdf/PDFViewer";
import { useLocalSearchParams, router } from "expo-router";
import { MessageCircle, Share, Trash2, ZoomIn, ZoomOut, RotateCw, AlertCircle, Bug } from "lucide-react-native";
import { useDocuments } from "@/hooks/useDocuments";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import { readPdfAsBase64 } from "@/utils/files";
import { Eye, Edit3, Save, X, FileText } from "lucide-react-native";

export default function PDFViewerScreen() {
  const { documentId } = useLocalSearchParams<{ documentId: string }>();
  const { documents, deleteDocument, updateDocument } = useDocuments();
  const [document, setDocument] = useState<any>(null);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [zoom, setZoom] = useState<number>(1);
  const [rotation, setRotation] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pdfBase64, setPdfBase64] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [showDebugInfo, setShowDebugInfo] = useState<boolean>(false);
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
  const pdfRef = useRef<PDFViewerRef>(null);

  // OCR-related state
  const [showTextOverlay, setShowTextOverlay] = useState<boolean>(false);
  const [showTextModal, setShowTextModal] = useState<boolean>(false);
  const [isEditingText, setIsEditingText] = useState<boolean>(false);
  const [editedText, setEditedText] = useState<string>('');
  const [isSavingText, setIsSavingText] = useState<boolean>(false);

  const currentDocument = useMemo(() => {
    return documents.find((d) => d.id === documentId);
  }, [documents, documentId]);

  const addDebugInfoRef = useRef((info: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const debugMessage = `[${timestamp}] ${info}`;
    console.log('PDF Debug:', debugMessage);
    setDebugInfo(prev => [...prev, debugMessage]);
  });

  const addDebugInfo = useCallback((info: string) => {
    addDebugInfoRef.current(info);
  }, []);

  const loadPDFContent = useCallback(async (doc: any) => {
    console.log('loadPDFContent called for document:', doc?.name);
    try {
      setIsLoading(true);
      setError(null);
      setDebugInfo([]);

      addDebugInfoRef.current(`Starting to load document: ${doc.name}`);
      addDebugInfoRef.current(`Document URI: ${doc.uri}`);
      addDebugInfoRef.current(`Is Scanned: ${doc.isScanned}`);
      addDebugInfoRef.current(`Platform: ${Platform.OS}`);
      addDebugInfoRef.current(`Document size: ${doc.size} bytes`);
      
      console.log('Loading document from URI:', doc.uri, 'isScanned:', doc.isScanned);
      
      if (Platform.OS === 'web') {
        addDebugInfoRef.current('Processing document on web platform');
        // On web, handle different URI formats
        if (doc.uri.startsWith('data:application/pdf;base64,')) {
          // Extract base64 from PDF data URL
          const base64 = doc.uri.split(',')[1];
          addDebugInfoRef.current(`PDF loaded from data URL, base64 size: ${base64.length}`);
          console.log('PDF loaded from data URL, size:', base64.length);
          setPdfBase64(base64);
        } else if (doc.uri.startsWith('data:image/')) {
          // Handle scanned images stored as data URLs
          addDebugInfoRef.current('Loading scanned image as document');
          console.log('Loading scanned image as document');
          setPdfBase64(doc.uri); // Store the full data URL for images
        } else if (doc.uri.startsWith('blob:')) {
          // Convert blob URL to base64
          addDebugInfoRef.current('Converting blob URL to base64');
          try {
            const response = await fetch(doc.uri);
            const blob = await response.blob();
            addDebugInfo(`Blob type: ${blob.type}, size: ${blob.size}`);
            const reader = new FileReader();
            
            reader.onload = () => {
              const result = reader.result as string;
              if (blob.type.startsWith('image/')) {
                // It's an image, store as full data URL
                addDebugInfoRef.current('Loaded scanned image from blob');
                console.log('Loaded scanned image from blob');
                setPdfBase64(result);
              } else {
                // It's a PDF, extract base64
                const base64 = result.split(',')[1];
                addDebugInfoRef.current(`PDF loaded from blob, base64 size: ${base64.length}`);
                console.log('PDF loaded from blob, size:', base64.length);
                setPdfBase64(base64);
              }
            };

            reader.onerror = () => {
              addDebugInfoRef.current('FileReader error occurred');
              throw new Error('Failed to read blob as base64');
            };
            
            reader.readAsDataURL(blob);
          } catch (blobError) {
            addDebugInfoRef.current(`Blob error: ${blobError}`);
            console.error('Error reading blob:', blobError);
            throw new Error('Failed to load document from blob');
          }
        } else {
          addDebugInfoRef.current(`Unsupported URI format on web: ${doc.uri}`);
          throw new Error('Unsupported URI format on web');
        }
      } else {
        addDebugInfoRef.current('Processing document on native platform');
        
        // Check if the document URI is already a data URL (stored from scanning)
        if (doc.uri.startsWith('data:')) {
          addDebugInfoRef.current('Document is already a data URL, using directly');
          setPdfBase64(doc.uri);
        } else {
          // Native platform - read file as base64 and keep raw base64 for PDFViewer
          addDebugInfoRef.current(`Attempting to read file at: ${doc.uri}`);

          try {
            // For both scanned and PDF documents, read as base64 and keep raw base64
            const base64 = await FileSystem.readAsStringAsync(doc.uri, {
              encoding: FileSystem.EncodingType.Base64,
            });
            addDebugInfoRef.current(`Document loaded as raw base64, size: ${base64.length}`);
            console.log('Document loaded as raw base64, size:', base64.length);
            setPdfBase64(base64);
          } catch (fileError) {
            addDebugInfoRef.current(`FileSystem error: ${fileError}`);
            console.error('FileSystem error:', fileError);

            // If FileSystem fails, check if it's a data URL that was stored incorrectly
            if (doc.uri.includes('base64')) {
              addDebugInfoRef.current('Attempting to use URI as base64 data');
              setPdfBase64(doc.uri);
            } else {
              throw new Error(`Failed to read file: ${fileError instanceof Error ? fileError.message : 'Unknown error'}`);
            }
          }
        }
      }
      addDebugInfoRef.current('Document loading completed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load document';
      addDebugInfoRef.current(`Error loading document: ${errorMessage}`);
      console.error('Error loading document:', err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []); // Removed addDebugInfo dependency to prevent infinite loop

  useEffect(() => {
    console.log('useEffect triggered, currentDocument:', currentDocument?.id);
    setDocument(currentDocument);

    if (currentDocument) {
      loadPDFContent(currentDocument);
    }
  }, [currentDocument, documentId]); // Removed loadPDFContent from dependencies

  const handleChatWithDocument = () => {
    router.push({
      pathname: "/(tabs)/chat",
      params: { documentId: document.id },
    });
  };

  const handleDeleteDocument = () => {
    if (!document?.id?.trim() || !document?.name?.trim()) return;
    
    if (Platform.OS === 'web') {
      const confirmed = confirm(`Are you sure you want to delete "${document.name.trim()}"?`);
      if (confirmed) {
        deleteDocument(document.id.trim());
        router.back();
      }
    } else {
      Alert.alert(
        'Delete Document',
        `Are you sure you want to delete "${document.name.trim()}"?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => {
              deleteDocument(document.id.trim());
              router.back();
            },
          },
        ]
      );
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.25, 3));
    pdfRef.current?.zoomIn();
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.25, 0.5));
    pdfRef.current?.zoomOut();
  };

  const handleRotate = () => {
    setRotation(prev => {
      const next = (prev + 90) % 360;
      pdfRef.current?.rotate(90);
      return next;
    });
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleShare = async () => {
    try {
      if (!document || !pdfBase64) return;
      if (!(await Sharing.isAvailableAsync())) {
        addDebugInfoRef.current('Sharing not available on this platform');
        if (Platform.OS === 'web') alert('Sharing not available');
        return;
      }
      const baseName = (document.name || 'document').replace(/\s+/g, '_');
      let fileUri = '';

      const isImage = pdfBase64.startsWith('data:image/');
      if (isImage) {
        // Image path
        const isDataUrl = pdfBase64.startsWith('data:');
        let base64 = pdfBase64;
        let ext = 'jpg';
        if (isDataUrl) {
          const [prefix, b64] = pdfBase64.split(',');
          base64 = b64;
          if (prefix.includes('png')) ext = 'png';
          else if (prefix.includes('jpeg')) ext = 'jpg';
        }
        fileUri = `${FileSystem.cacheDirectory}${baseName}.${ext}`;
        await FileSystem.writeAsStringAsync(fileUri, base64, { encoding: FileSystem.EncodingType.Base64 });
      } else {
        // PDF path (base64 without data prefix)
        const base64 = pdfBase64.startsWith('data:') ? pdfBase64.split(',')[1] : pdfBase64;
        fileUri = `${FileSystem.cacheDirectory}${baseName}.pdf`;
        await FileSystem.writeAsStringAsync(fileUri, base64, { encoding: FileSystem.EncodingType.Base64 });
      }

      await Sharing.shareAsync(fileUri, { dialogTitle: 'Share document' });
      addDebugInfoRef.current('Share dialog opened');
    } catch (e) {
      console.error('Share error:', e);
      addDebugInfoRef.current(`Share error: ${e instanceof Error ? e.message : String(e)}`);
    }
  };

  const testPDFViewer = () => {
    addDebugInfoRef.current('=== Starting PDF Viewer Test ===');
    addDebugInfoRef.current(`Screen dimensions: ${screenWidth}x${screenHeight}`);
    addDebugInfoRef.current(`Current zoom: ${zoom}`);
    addDebugInfoRef.current(`Current rotation: ${rotation}`);
    addDebugInfoRef.current(`PDF data available: ${!!pdfBase64}`);
    addDebugInfoRef.current(`PDF data length: ${pdfBase64?.length || 0}`);
    addDebugInfoRef.current(`Document type: ${document?.isScanned ? 'Scanned' : 'PDF'}`);

    if (pdfBase64) {
      const isImage = pdfBase64.startsWith('data:image/');
      addDebugInfoRef.current(`Detected as: ${isImage ? 'Image' : 'PDF'}`);

      // Test base64 validity
      try {
        if (isImage) {
          addDebugInfoRef.current('Testing image data URL format');
          const img = new Image();
          img.onload = () => addDebugInfoRef.current('Image data URL is valid');
          img.onerror = () => addDebugInfoRef.current('Image data URL is invalid');
          img.src = pdfBase64;
        } else {
          addDebugInfoRef.current('Testing PDF base64 format');
          const base64Data = pdfBase64.startsWith('data:') ? pdfBase64.split(',')[1] : pdfBase64;
          const binaryString = atob(base64Data.substring(0, 100)); // Test first 100 chars
          addDebugInfoRef.current(`PDF header test: ${binaryString.substring(0, 10)}`);
        }
      } catch (testError) {
        addDebugInfoRef.current(`Base64 test error: ${testError}`);
      }
    }

    addDebugInfoRef.current('=== PDF Viewer Test Complete ===');
  };

  // OCR-related handlers
  const handleTextOverlayToggle = () => {
    setShowTextOverlay(!showTextOverlay);
  };

  const handleViewText = () => {
    if (document?.ocrText) {
      setEditedText(document.ocrText);
      setIsEditingText(false);
      setShowTextModal(true);
    } else {
      Alert.alert('No Text Found', 'No OCR text was extracted from this document.');
    }
  };

  const handleEditText = () => {
    setIsEditingText(true);
  };

  const handleCancelEdit = () => {
    setIsEditingText(false);
    setEditedText(document?.ocrText || '');
  };

  const handleSaveText = async () => {
    if (!document?.id || !editedText.trim()) {
      Alert.alert('Error', 'Text cannot be empty');
      return;
    }

    setIsSavingText(true);
    try {
      await updateDocument({
        id: document.id,
        updates: {
          ocrText: editedText.trim(),
          ocrEdited: true,
        },
      });

      // Update local document state
      setDocument(prev => prev ? { ...prev, ocrText: editedText.trim(), ocrEdited: true } : null);
      setIsEditingText(false);
      setShowTextModal(false);

      Alert.alert('Success', 'Text has been saved successfully');
    } catch (error) {
      console.error('Error saving text:', error);
      Alert.alert('Error', 'Failed to save text. Please try again.');
    } finally {
      setIsSavingText(false);
    }
  };

  const getDocumentViewerHTML = (data: string) => {
    const isImage = data.startsWith('data:image/');
    const isDataUrl = data.startsWith('data:');

    // Avoid updating state during render; log to console instead
    console.log('PDF Debug:', `Creating HTML viewer - isImage: ${isImage}, isDataUrl: ${isDataUrl}`);

    if (isImage) {
      // Display scanned image
      const imageSrc = isDataUrl ? data : `data:image/jpeg;base64,${data}`;
      console.log('PDF Debug:', `Image viewer - src length: ${imageSrc.length}`);
      return `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
            <title>Document Viewer</title>
            <style>
              * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
              }
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background-color: #f5f5f5;
                overflow: auto;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
                padding: 10px;
              }
              .image-container {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                overflow: hidden;
                transform: scale(${zoom}) rotate(${rotation}deg);
                transition: transform 0.3s ease;
                max-width: 100%;
                max-height: 90vh;
              }
              .document-image {
                max-width: 100%;
                max-height: 90vh;
                object-fit: contain;
                display: block;
              }
              .debug-info {
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
                max-width: 300px;
                z-index: 1000;
              }
            </style>
          </head>
          <body>
            <div class="debug-info" id="debug">Loading image...</div>
            <div class="image-container">
              <img src="${imageSrc}" alt="Scanned Document" class="document-image" 
                   onload="imageLoaded()" onerror="imageError()" />
            </div>
            <script>
              function sendMessage(message) {
                if (window.ReactNativeWebView) {
                  window.ReactNativeWebView.postMessage(message);
                }
                console.log('Image Viewer:', message);
              }
              
              function updateDebug(message) {
                document.getElementById('debug').textContent = message;
                sendMessage(message);
              }
              
              function imageLoaded() {
                updateDebug('Image loaded successfully');
                setTimeout(() => {
                  document.getElementById('debug').style.display = 'none';
                }, 2000);
              }
              
              function imageError() {
                updateDebug('Image failed to load');
              }
              
              updateDebug('Image viewer initialized');
              sendMessage('Image data length: ${imageSrc.length}');
            </script>
          </body>
        </html>
      `;
    } else {
      // Display PDF
      const pdfData = isDataUrl ? data.split(',')[1] : data;
      console.log('PDF Debug:', `PDF viewer - data length: ${pdfData.length}`);
      
      return `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
            <title>PDF Viewer</title>
            <style>
              * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
              }
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background-color: #f5f5f5;
                overflow: auto;
                min-height: 100vh;
              }
              .container {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 10px;
                min-height: 100vh;
              }
              .pdf-container {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                overflow: hidden;
                transform: scale(${zoom}) rotate(${rotation}deg);
                transition: transform 0.3s ease;
                width: 100%;
                max-width: ${Math.floor(screenWidth * 0.95)}px;
                min-height: ${Math.floor(screenHeight * 0.6)}px;
              }
              .pdf-embed {
                width: 100%;
                height: ${Math.floor(screenHeight * 0.6)}px;
                border: none;
                display: block;
              }
              .loading {
                display: flex;
                align-items: center;
                justify-content: center;
                height: ${Math.floor(screenHeight * 0.6)}px;
                color: #666;
                font-size: 16px;
                background: white;
              }
              .error {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: ${Math.floor(screenHeight * 0.6)}px;
                color: #e74c3c;
                text-align: center;
                padding: 20px;
                background: white;
              }
              .error h3 {
                margin-bottom: 10px;
                font-size: 18px;
              }
              .error p {
                font-size: 14px;
                line-height: 1.5;
                margin-bottom: 10px;
              }
              .debug-info {
                position: fixed;
                top: 10px;
                right: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
                max-width: 300px;
                z-index: 1000;
              }
              .fallback-link {
                color: #3B82F6;
                text-decoration: underline;
                cursor: pointer;
                margin-top: 10px;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="debug-info" id="debug">Initializing PDF viewer...</div>
              <div class="pdf-container">
                <div class="loading" id="loading">Loading PDF...</div>
                <iframe 
                  id="pdf-iframe"
                  class="pdf-embed"
                  src="data:application/pdf;base64,${pdfData}"
                  style="display: none;"
                  onload="pdfLoaded()"
                  onerror="pdfError()"
                ></iframe>
              </div>
            </div>
            <script>
              let loadAttempts = 0;
              let maxAttempts = 3;
              
              function sendMessage(message) {
                if (window.ReactNativeWebView) {
                  window.ReactNativeWebView.postMessage(message);
                }
                console.log('PDF Viewer:', message);
              }
              
              function updateDebug(message) {
                const debugEl = document.getElementById('debug');
                if (debugEl) {
                  debugEl.textContent = message;
                }
                sendMessage(message);
              }
              
              function pdfLoaded() {
                updateDebug('PDF loaded successfully');
                document.getElementById('loading').style.display = 'none';
                document.getElementById('pdf-iframe').style.display = 'block';
                setTimeout(() => {
                  const debugEl = document.getElementById('debug');
                  if (debugEl) debugEl.style.display = 'none';
                }, 3000);
              }
              
              function pdfError() {
                loadAttempts++;
                updateDebug('PDF load attempt ' + loadAttempts + ' failed');
                
                if (loadAttempts < maxAttempts) {
                  setTimeout(tryAlternativeMethod, 1000);
                } else {
                  showFinalError();
                }
              }
              
              function tryAlternativeMethod() {
                updateDebug('Trying alternative PDF display method...');
                
                const container = document.querySelector('.pdf-container');
                const pdfDataUrl = 'data:application/pdf;base64,${pdfData}';
                
                // Try object tag
                container.innerHTML = \`
                  <object 
                    data="\${pdfDataUrl}"
                    type="application/pdf"
                    width="100%"
                    height="${Math.floor(screenHeight * 0.6)}px"
                    onload="pdfLoaded()"
                  >
                    <embed 
                      src="\${pdfDataUrl}"
                      type="application/pdf"
                      width="100%"
                      height="${Math.floor(screenHeight * 0.6)}px"
                    />
                  </object>
                \`;
                
                setTimeout(() => {
                  if (loadAttempts >= maxAttempts) {
                    showFinalError();
                  }
                }, 3000);
              }
              
              function showFinalError() {
                updateDebug('All PDF display methods failed');
                document.getElementById('loading').innerHTML = \`
                  <div class="error">
                    <h3>Unable to Display PDF</h3>
                    <p>This PDF cannot be displayed in the current browser.</p>
                    <p>PDF data length: ${pdfData.length} characters</p>
                    <p>Platform: \${navigator.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'}</p>
                    <div class="fallback-link" onclick="downloadPDF()">Download PDF</div>
                  </div>
                \`;
              }
              
              function downloadPDF() {
                try {
                  const link = document.createElement('a');
                  link.href = 'data:application/pdf;base64,${pdfData}';
                  link.download = 'document.pdf';
                  link.click();
                  updateDebug('PDF download initiated');
                } catch (e) {
                  updateDebug('PDF download failed: ' + e.message);
                }
              }
              
              // Initialize
              updateDebug('PDF viewer initialized');
              sendMessage('PDF data length: ${pdfData.length}');
              sendMessage('PDF data preview: ' + '${pdfData}'.substring(0, 50));
              
              // Auto-hide debug after 5 seconds if successful
              setTimeout(() => {
                const iframe = document.getElementById('pdf-iframe');
                if (iframe && iframe.style.display !== 'none') {
                  const debugEl = document.getElementById('debug');
                  if (debugEl) debugEl.style.display = 'none';
                } else if (loadAttempts === 0) {
                  // If iframe hasn't loaded yet, trigger error handling
                  pdfError();
                }
              }, 5000);
            </script>
          </body>
        </html>
      `;
    }
  };

  if (!document) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <AlertCircle color="#EF4444" size={48} />
          <Text style={styles.errorText}>Document not found</Text>
        </View>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={styles.loadingText}>Loading PDF...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <AlertCircle color="#EF4444" size={48} />
          <Text style={styles.errorText}>Error loading PDF</Text>
          <Text style={styles.errorDescription}>{error}</Text>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={() => document && loadPDFContent(document)}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (!pdfBase64) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <AlertCircle color="#EF4444" size={48} />
          <Text style={styles.errorText}>PDF content not available</Text>
        </View>
      </View>
    );
  }

  if (isFullscreen) {
    return (
      <View style={styles.fullscreenContainer}>
        <View style={styles.fullscreenHeader}>
          <TouchableOpacity
            style={styles.fullscreenButton}
            onPress={toggleFullscreen}
          >
            <Text style={styles.fullscreenButtonText}>Exit Fullscreen</Text>
          </TouchableOpacity>
        </View>
        <PDFViewer
          ref={pdfRef}
          source={{
            kind: !String(pdfBase64).startsWith('data:image/') ? 'pdf' : 'image',
            data: pdfBase64
          }}
          initialZoom={zoom}
          rotation={rotation}
          style={styles.fullscreenWebView}
          onError={(msg) => setError(msg)}
          onReady={() => addDebugInfoRef.current('PDF viewer ready (fullscreen)')}
          onMetrics={(m) => {
            addDebugInfoRef.current(`Pages: ${m.pageCount ?? '?'} (fullscreen)`);
            if (m.scale !== undefined) {
              setZoom(m.scale);
            }
            if (m.currentPage !== undefined) {
              setCurrentPage(m.currentPage);
            }
          }}
          textOverlayData={document?.ocrPages}
          showTextOverlay={showTextOverlay}
          onTextOverlayToggle={handleTextOverlayToggle}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.documentTitle} numberOfLines={1}>
          {document.name}
        </Text>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleZoomOut}
          >
            <ZoomOut color="#6B7280" size={18} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleZoomIn}
          >
            <ZoomIn color="#6B7280" size={18} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleRotate}
          >
            <RotateCw color="#6B7280" size={18} />
          </TouchableOpacity>

          {document?.ocrProcessed && (
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleTextOverlayToggle}
            >
              <Eye color={showTextOverlay ? "#3B82F6" : "#6B7280"} size={18} />
            </TouchableOpacity>
          )}

          {document?.ocrProcessed && (
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleViewText}
            >
              <FileText color="#3B82F6" size={18} />
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleChatWithDocument}
          >
            <MessageCircle color="#3B82F6" size={18} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
            <Share color="#6B7280" size={18} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowDebugInfo(!showDebugInfo)}
          >
            <Bug color="#10B981" size={18} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleDeleteDocument}
          >
            <Trash2 color="#EF4444" size={18} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.pdfContainer}>
          <TouchableOpacity 
            style={styles.fullscreenToggle}
            onPress={toggleFullscreen}
          >
            <Text style={styles.fullscreenToggleText}>View Fullscreen</Text>
          </TouchableOpacity>
          
          <PDFViewer
            ref={pdfRef}
            source={{
              kind: !String(pdfBase64).startsWith('data:image/') ? 'pdf' : 'image',
              data: pdfBase64
            }}
            initialZoom={zoom}
            rotation={rotation}
            style={[styles.pdfWebView, { height: Math.floor(screenHeight * 0.5) }]}
            onError={(msg) => setError(msg)}
            onReady={() => addDebugInfoRef.current('PDF viewer ready (embedded)')}
            onMetrics={(m) => {
              addDebugInfoRef.current(`Pages: ${m.pageCount ?? '?'}`);
              if (m.scale !== undefined) {
                setZoom(m.scale);
              }
              if (m.currentPage !== undefined) {
                setCurrentPage(m.currentPage);
              }
            }}
            textOverlayData={document?.ocrPages}
            showTextOverlay={showTextOverlay}
            onTextOverlayToggle={handleTextOverlayToggle}
          />
        </View>

        <View style={styles.documentDetails}>
          <Text style={styles.detailsTitle}>Document Details</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Name:</Text>
            <Text style={styles.detailValue}>{document.name}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Size:</Text>
            <Text style={styles.detailValue}>
              {(document.size / 1024 / 1024).toFixed(2)} MB
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Type:</Text>
            <Text style={styles.detailValue}>
              {document.isScanned ? 'Scanned PDF' : 'PDF Document'}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Added:</Text>
            <Text style={styles.detailValue}>
              {new Date(document.createdAt).toLocaleDateString()}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Zoom:</Text>
            <Text style={styles.detailValue}>{Math.round(zoom * 100)}%</Text>
          </View>
        </View>

        {showDebugInfo && (
          <View style={styles.debugContainer}>
            <Text style={styles.debugTitle}>Debug Information</Text>
            <ScrollView style={styles.debugScrollView} nestedScrollEnabled={true}>
              {debugInfo.map((info, index) => (
                <Text key={`debug-${index}-${info.substring(0, 20)}`} style={styles.debugText}>
                  {info}
                </Text>
              ))}
            </ScrollView>
            <TouchableOpacity
              style={styles.testButton}
              onPress={() => testPDFViewer()}
            >
              <Text style={styles.testButtonText}>Run PDF Test</Text>
            </TouchableOpacity>
          </View>
        )}
        
        <TouchableOpacity
          style={styles.chatButton}
          onPress={handleChatWithDocument}
        >
          <MessageCircle color="#FFFFFF" size={20} />
          <Text style={styles.chatButtonText}>Chat with this Document</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Text Modal */}
      <Modal
        visible={showTextModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowTextModal(false)}
      >
        <View style={styles.textModalContainer}>
          <View style={styles.textModalHeader}>
            <Text style={styles.textModalTitle}>
              {isEditingText ? 'Edit Text' : 'Extracted Text'}
            </Text>
            <TouchableOpacity
              style={styles.textModalCloseButton}
              onPress={() => setShowTextModal(false)}
            >
              <X color="#6B7280" size={24} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.textModalContent}>
            {isEditingText ? (
              <TextInput
                style={styles.textInput}
                value={editedText}
                onChangeText={setEditedText}
                multiline
                textAlignVertical="top"
                placeholder="Enter text..."
              />
            ) : (
              <Text style={styles.textContent}>
                {editedText || 'No text available'}
              </Text>
            )}
          </ScrollView>

          <View style={styles.textModalActions}>
            {isEditingText ? (
              <>
                <TouchableOpacity
                  style={[styles.textModalButton, styles.cancelButton]}
                  onPress={handleCancelEdit}
                  disabled={isSavingText}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.textModalButton, styles.saveButton]}
                  onPress={handleSaveText}
                  disabled={isSavingText || !editedText.trim()}
                >
                  {isSavingText ? (
                    <ActivityIndicator size="small" color="#FFFFFF" />
                  ) : (
                    <Save color="#FFFFFF" size={16} />
                  )}
                  <Text style={styles.saveButtonText}>Save</Text>
                </TouchableOpacity>
              </>
            ) : (
              <TouchableOpacity
                style={[styles.textModalButton, styles.editButton]}
                onPress={handleEditText}
              >
                <Edit3 color="#FFFFFF" size={16} />
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
  },
  documentTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginRight: 16,
  },
  headerActions: {
    flexDirection: "row",
    gap: 6,
  },
  headerButton: {
    width: 36,
    height: 36,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    backgroundColor: "#F3F4F6",
  },
  content: {
    flex: 1,
  },
  pdfContainer: {
    margin: 20,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    overflow: "hidden",
  },
  fullscreenToggle: {
    backgroundColor: "#3B82F6",
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: "center",
  },
  fullscreenToggleText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
  pdfWebView: {
    backgroundColor: "#FFFFFF",
  },
  fullscreenContainer: {
    flex: 1,
    backgroundColor: "#000000",
  },
  fullscreenHeader: {
    position: "absolute",
    top: Platform.OS === 'ios' ? 50 : 30,
    right: 20,
    zIndex: 1000,
  },
  fullscreenButton: {
    backgroundColor: "rgba(0,0,0,0.7)",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  fullscreenButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
  fullscreenWebView: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  documentDetails: {
    margin: 20,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6",
  },
  detailLabel: {
    fontSize: 16,
    color: "#6B7280",
    fontWeight: "500",
  },
  detailValue: {
    fontSize: 16,
    color: "#1F2937",
    fontWeight: "400",
    flex: 1,
    textAlign: "right",
  },
  chatButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#3B82F6",
    marginHorizontal: 20,
    marginBottom: 20,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  chatButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  errorText: {
    fontSize: 18,
    color: "#6B7280",
    marginTop: 16,
    textAlign: "center",
  },
  errorDescription: {
    fontSize: 14,
    color: "#9CA3AF",
    marginTop: 8,
    textAlign: "center",
    paddingHorizontal: 20,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#6B7280",
    marginTop: 16,
  },
  retryButton: {
    backgroundColor: "#3B82F6",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  debugContainer: {
    margin: 20,
    backgroundColor: "#1F2937",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#374151",
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#10B981",
    marginBottom: 12,
  },
  debugScrollView: {
    maxHeight: 200,
    backgroundColor: "#111827",
    borderRadius: 8,
    padding: 12,
  },
  debugText: {
    fontSize: 12,
    color: "#D1D5DB",
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
    marginBottom: 4,
  },
  testButton: {
    backgroundColor: "#10B981",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 12,
    alignItems: "center",
  },
  testButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
  // Text Modal Styles
  textModalContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  textModalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
  },
  textModalTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1F2937",
  },
  textModalCloseButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
    backgroundColor: "#F3F4F6",
  },
  textModalContent: {
    flex: 1,
    padding: 20,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: "#1F2937",
    lineHeight: 24,
    padding: 16,
    backgroundColor: "#F9FAFB",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    minHeight: 200,
  },
  textContent: {
    fontSize: 16,
    color: "#1F2937",
    lineHeight: 24,
  },
  textModalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 20,
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#E5E7EB",
    gap: 12,
  },
  textModalButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  cancelButton: {
    backgroundColor: "#F3F4F6",
  },
  cancelButtonText: {
    color: "#6B7280",
    fontSize: 16,
    fontWeight: "600",
  },
  saveButton: {
    backgroundColor: "#3B82F6",
  },
  saveButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  editButton: {
    backgroundColor: "#3B82F6",
  },
  editButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});
